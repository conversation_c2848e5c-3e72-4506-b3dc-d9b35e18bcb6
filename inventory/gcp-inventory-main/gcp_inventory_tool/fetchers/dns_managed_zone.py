# # --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---
# import logging
# from typing import List, Dict, Any, Optional
# from google.oauth2.credentials import Credentials
# from google.cloud import dns
# from google.api_core import exceptions as api_exceptions

# from ..utils.resource_name import get_resource_name
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')


# class DnsManagedZoneFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud DNS Managed Zone details for a project.
#     """
#     SERVICE_NAME = "dns_managed_zone" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Cloud DNS Managed Zones in the specified project.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud DNS Managed Zone fetch...")
#         inventory = []
#         client = dns.ManagedZonesClient(credentials=credentials)

#         try:
#             zones = client.list_managed_zones(project=project_id)
#             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managed zones...")

#             for zone in zones:
#                 # Prepare info dictionary based on PowerShell script's fields
#                 info = {
#                     "Project": project_id,
#                     "Name": zone.name,
#                     "Description": zone.description,
#                     "Visibility": str(zone.visibility), # PUBLIC or PRIVATE
#                     "Networks": [], # Populated below (for private zones)
#                     "Forwarders": [], # Populated below (for forwarding zones)
#                     "CreationTime": zone.creation_time,
#                     "DnsName": zone.dns_name, # Added field
#                     "NameServers": list(zone.name_servers) if zone.name_servers else [], # Added field
#                     "DnssecEnabled": zone.dnssec_config.state == dns.types.DnsKeySpec.KeyType.KEY_SIGNING if zone.dnssec_config else False, # Added field
#                     "LoggingEnabled": zone.logging_config.enable_logging if zone.logging_config else False, # Added field
#                     "service": self.SERVICE_NAME
#                 }

#                 # Private Zone Network Config
#                 if zone.properties.privateVisibilityConfig and zone.properties.privateVisibilityConfig.networks:
#                     info["Networks"] = [
#                         get_resource_name(net.network_url)
#                         for net in zone.properties.privateVisibilityConfig.networks if net.network_url
#                     ]

#                 # Forwarding Config
#                 if zone.forwarding_config and zone.forwarding_config.target_name_servers:
#                     info["Forwarders"] = [
#                         ns.ipv4_address
#                         for ns in zone.forwarding_config.target_name_servers if ns.ipv4_address
#                         # Add ipv6_address if needed: ns.ipv6_address
#                     ]

#                 inventory.append(info)

#         except api_exceptions.Forbidden as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing DNS zones or accessing DNS API. Ensure API is enabled and necessary roles granted (e.g., 'DNS Reader'). Details: {e}")
#             return [] # Return empty list on permission errors
#         except api_exceptions.NotFound as e:
#              # This might indicate the API isn't enabled or project has no DNS zones
#              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud DNS API might not be enabled or project not found / no zones exist. Details: {e}")
#              return []
#         except Exception as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process DNS Managed Zones: {e}", exc_info=True)
#             return [] # Fail gracefully
#         finally:
#             try:
#                 client.transport.close()
#             except Exception:
#                 pass

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud DNS Managed Zone fetch. Found {len(inventory)} zones.")
#         return inventory

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import dns
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class DnsManagedZoneFetcher(ServiceFetcher):
    """
    Fetches Google Cloud DNS Managed Zone details for a project.
    """
    SERVICE_NAME = "dns_managed_zone" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud DNS Managed Zones in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud DNS Managed Zone fetch...")
        inventory = []
        
        # Fix: Use the correct DNS client
        client = dns.Client(project=project_id, credentials=credentials)

        try:
            # Fix: Use the correct method to list zones
            zones = client.list_zones()
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managed zones...")

            for zone in zones:
                # Determine visibility based on private_zone attribute
                # ManagedZone does not have a 'visibility' attribute
                logger.info(str(zone.__dict__))
                # visibility = "PRIVATE" if hasattr(zone, 'private_zone') and zone.private_zone else "PUBLIC"
                
                # Determine visibility based on private_visibility_config
                visibility = "PRIVATE" if hasattr(zone, 'private_visibility_config') and zone.private_visibility_config else "PUBLIC"
                
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": zone.name,
                    "Description": zone.description if hasattr(zone, 'description') else "",
                    "Visibility": visibility,
                    "Networks": [], # Will be populated below for private zones
                    "Forwarders": [], # Will be populated below for forwarding zones
                    "CreationTime": zone.created.isoformat() if hasattr(zone, 'created') else None,
                    "DnsName": zone.dns_name,
                    "NameServers": zone.name_servers if hasattr(zone, 'name_servers') else [],
                    "DnssecEnabled": False,  # Will set properly below
                    "LoggingEnabled": False,  # Will set properly below
                    "service": self.SERVICE_NAME
                }

                # Check for other attributes carefully with hasattr
                # Private Zone Network Config
                if hasattr(zone, 'private_visibility_config') and zone.private_visibility_config:
                    try:
                        networks = zone.private_visibility_config.get('networks', [])
                        info["Networks"] = [
                            get_resource_name(net.get('networkUrl', ''))
                            for net in networks if 'networkUrl' in net
                        ]
                    except AttributeError:
                        # Handle case where it's a property not a dict
                        if hasattr(zone.private_visibility_config, 'networks'):
                            networks = zone.private_visibility_config.networks or []
                            info["Networks"] = [
                                get_resource_name(net.network_url) 
                                for net in networks if hasattr(net, 'network_url') and net.network_url
                            ]

                # Forwarding Config
                if hasattr(zone, 'forwarding_config') and zone.forwarding_config:
                    try:
                        name_servers = zone.forwarding_config.get('targetNameServers', [])
                        info["Forwarders"] = [
                            ns.get('ipv4Address', '')
                            for ns in name_servers if 'ipv4Address' in ns
                        ]
                    except AttributeError:
                        # Handle case where it's a property not a dict
                        if hasattr(zone.forwarding_config, 'target_name_servers'):
                            name_servers = zone.forwarding_config.target_name_servers or []
                            info["Forwarders"] = [
                                ns.ipv4_address 
                                for ns in name_servers if hasattr(ns, 'ipv4_address') and ns.ipv4_address
                            ]

                # DNSSEC config
                if hasattr(zone, 'dnssec_config') and zone.dnssec_config:
                    try:
                        info["DnssecEnabled"] = zone.dnssec_config.get('state') == 'on'
                    except AttributeError:
                        if hasattr(zone.dnssec_config, 'state'):
                            info["DnssecEnabled"] = getattr(zone.dnssec_config, 'state') == 'on'

                # Logging config
                if hasattr(zone, 'logging_config') and zone.logging_config:
                    try:
                        info["LoggingEnabled"] = zone.logging_config.get('enableLogging', False)
                    except AttributeError:
                        if hasattr(zone.logging_config, 'enable_logging'):
                            info["LoggingEnabled"] = bool(getattr(zone.logging_config, 'enable_logging', False))

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing DNS zones or accessing DNS API. Ensure API is enabled and necessary roles granted (e.g., 'DNS Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled or project has no DNS zones
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud DNS API might not be enabled or project not found / no zones exist. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process DNS Managed Zones: {e}", exc_info=True)
            return [] # Fail gracefully

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud DNS Managed Zone fetch. Found {len(inventory)} zones.")
        return inventory
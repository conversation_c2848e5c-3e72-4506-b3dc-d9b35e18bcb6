Resource-Level Findings Expansion

============================================================
🎯 Objective
============================================================
You are a cloud migration analyst.
You are given two inputs:
1.  **Analysis Summary**:
    ```
    {analysis_summary}
    ```
2.  **Inventory Snapshot**:
    ```
    {inventory_snapshot}
    ```

Your task is to expand the high-level issues from the **Analysis Summary** into per-resource findings, using the **Inventory Snapshot** to list each resource individually.
Each resource listed in the inventory must have one row in the findings sheet.

============================================================
📝 Output Requirements
============================================================
- Always output in tabular format.  
- Use only these columns in this exact order:

  Project ID  
  Resource  
  Resource Type  
  Category  
  Action
  Description  
 

- One row = one resource.  
- If the analysis summary says “10 instances” and the inventory lists 10 IDs, generate 10 rows (one per instance).  
- Do not add extra columns or commentary.  
- Description must combine:  
  - Technical challenge  
  - Business impact  
  - Recommendation  
- Tone: professional, precise, migration-focused.  

============================================================
📊 Few-Shot Examples
============================================================

Example 1 — AWS (RDS PostgreSQL)
--------------------------------
Analysis Summary:
- AWS RDS PostgreSQL — Multi-AZ features differ from Cloud SQL HA.  

Inventory Extract:
- rds-prod-db01  
- rds-erp-db02  

Output:
| Project ID    | Resource      | Resource Type      | Category            | Action                 | Description                                                                                                |
|---------------|---------------|--------------------|---------------------|------------------------|------------------------------------------------------------------------------------------------------------|
| finance-prj   | rds-prod-db01 | AWS RDS PostgreSQL | Database Resilience | Implement Cloud SQL HA | Instance relies on AWS Multi-AZ; must be re-architected to Cloud SQL HA with synchronous failover testing. | 
| erp-prj       | rds-erp-db02  | AWS RDS PostgreSQL | Database Resilience | Implement Cloud SQL HA | Instance relies on AWS Multi-AZ; must be re-architected to Cloud SQL HA with synchronous failover testing. | 


Example 2 — GCP (Cloud Storage Buckets)
---------------------------------------
Analysis Summary:
- Cloud Storage Buckets — Lifecycle policies differ from AWS S3 tiering.  

Inventory Extract:
- gcs-data-archive  
- gcs-logs-backup  

Output:
| Project ID      | Resource         | Resource Type     | Category       | Action                   | Description                                                                                                   | 
|-----------------|------------------|-------------------|----------------|--------------------------|---------------------------------------------------------------------------------------------------------------|
| analytics-prj   | gcs-data-archive | GCP Cloud Storage | Storage & Cost | Align Lifecycle Policies | Bucket lifecycle rules not aligned with S3 tiering; must reconfigure lifecycle and retention to avoid cost overruns.| 
| logging-prj     | gcs-logs-backup  | GCP Cloud Storage | Storage & Cost | Align Lifecycle Policies | Bucket lifecycle rules not aligned with S3 tiering; must reconfigure lifecycle and retention to avoid cost overruns.| 

Example 3 — Azure (Virtual Network Gateways)
--------------------------------------------
Analysis Summary:
- Azure VNet Gateway — Not directly portable; must use Cloud VPN/Interconnect.  

Inventory Extract:
- vnet-gateway-prod  
- vnet-gateway-dr  

Output:
| Project ID | Resource           | Resource Type      | Category                  | Action              | Description                                                                                                | 
|------------|--------------------|--------------------|---------------------------|---------------------|------------------------------------------------------------------------------------------------------------|
| core-prj   | vnet-gateway-prod  | Azure VNet Gateway | Networking Paradigm Shift | Provision Cloud VPN | Gateway is Azure-specific; must migrate to Cloud VPN or Interconnect with GCP routing integration. | 
| dr-prj     | vnet-gateway-dr    | Azure VNet Gateway | Networking Paradigm Shift | Provision Cloud VPN | Gateway is Azure-specific; must migrate to Cloud VPN or Interconnect with GCP routing integration. | 


Example 4 — GKE (Cluster with Calico Policies)
----------------------------------------------
Analysis Summary:
- GKE Cluster — Calico network policies require validation for migration.  

Inventory Extract:
- gke-prod-cluster  
- gke-test-cluster  

Output:
| Project ID | Resource          | Resource Type | Category              | Action                | Description                                                                                                | 
|------------|-------------------|---------------|-----------------------|-----------------------|------------------------------------------------------------------------------------------------------------|
| ops-prj    | gke-prod-cluster  | GKE Cluster   | Security & Compliance | Test Calico Policies  | Cluster relies on Calico policies; must export, test, and reapply policies in GKE to avoid exposure. | 
| dev-prj    | gke-test-cluster  | GKE Cluster   | Security & Compliance | Test Calico Policies  | Cluster relies on Calico policies; must export, test, and reapply policies in GKE to avoid exposure. | 

============================================================
✅ Final Instructions
============================================================
- Always expand service-level findings into per-resource rows.  
- Use the exact schema: Project ID, Resource, Resource Type, Category, Action, Description.  
- Cover every resource in the inventory, even if they share the same issue.  
- Keep the Description clear, migration-focused, and actionable.

Analyze and respond based only on the data provided in the two sections above.

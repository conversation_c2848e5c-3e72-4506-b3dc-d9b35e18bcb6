import pandas as pd
import os

def gta_m2vm1(request,rootpath):
    moveg = request.form['move-group']
    file1 = request.files['csv-file']
    file1.save(os.path.join(rootpath,"migration", file1.filename))
    file2 = request.files['excel-file']
    file2.save(os.path.join(rootpath,"migration", file2.filename))

    file1_path = os.path.join(rootpath,"migration", file1.filename)
    file2_path = os.path.join(rootpath,"migration", file2.filename)
    out_path = os.path.join(rootpath,"migration", "ams-import.csv")


    export_df = pd.read_csv(file1_path)
    mapping_df = pd.read_csv(file2_path)
    error = ""

    try:
        # Use the exact template columns from the original export file
        template_columns = [
            "mgn:account-id",
            "mgn:region",
            "mgn:wave:name",
            "mgn:wave:description",
            "mgn:app:name",
            "mgn:app:description",
            "mgn:server:user-provided-id",
            "mgn:server:platform",
            "mgn:server:fqdn-for-action-framework",
            "mgn:launch:nic:0:network-interface-id",
            "mgn:launch:nic:0:subnet-id",
            "mgn:launch:nic:0:security-group-id:0",
            "mgn:launch:nic:0:private-ip:0",
            "mgn:launch:instance-type",
            "mgn:launch:transfer-server-tags",
            "mgn:launch:placement:tenancy",
            "mgn:launch:iam-instance-profile:name",
            "mgn:launch:placement:host-id",
            "mgn:launch:volume:/dev/xvda:type"
        ]

        # Filter mapping data for the selected movegroup
        mapping_df = mapping_df[mapping_df["Movegroup"] == moveg]

        # Create new rows from mapping data instead of trying to match existing rows
        new_rows = []

        for i in range(len(mapping_df)):
            # Create a new row with the template structure
            new_row = {}

            # Fill required basic server information
            new_row["mgn:server:user-provided-id"] = mapping_df.iloc[i]["Name"]
            new_row["mgn:app:name"] = moveg
            new_row["mgn:launch:nic:0:subnet-id"] = mapping_df.iloc[i]["Subnet Id"]
            new_row["mgn:launch:nic:0:security-group-id:0"] = mapping_df.iloc[i]["Security Group Id"]
            new_row["mgn:launch:instance-type"] = mapping_df.iloc[i]["Target Size"]

            # Set server platform based on OS (required field)
            if pd.notna(mapping_df.iloc[i]["OS"]):
                os_type = str(mapping_df.iloc[i]["OS"]).lower()
                if "windows" in os_type:
                    new_row["mgn:server:platform"] = "WINDOWS"
                else:
                    new_row["mgn:server:platform"] = "LINUX"
            else:
                new_row["mgn:server:platform"] = "LINUX"  # Default to LINUX

            # Set transfer server tags to true (required for launch template)
            new_row["mgn:launch:transfer-server-tags"] = "true"

            # Add Target Internal IP if provided
            if pd.notna(mapping_df.iloc[i]["Target Internal IP"]):
                new_row["mgn:launch:nic:0:private-ip:0"] = mapping_df.iloc[i]["Target Internal IP"]

            # Add IAM Profile if provided
            if pd.notna(mapping_df.iloc[i]["IAM-Profile"]):
                new_row["mgn:launch:iam-instance-profile:name"] = mapping_df.iloc[i]["IAM-Profile"]

            # Process tags from Tags column
            if pd.notna(mapping_df.iloc[i]["Tags"]):
                split_tags = str(mapping_df.iloc[i]["Tags"]).split('\n')
                for tag_line in split_tags:
                    if ':' in tag_line:
                        tag_parts = tag_line.split(':', 1)  # Split only on first colon
                        if len(tag_parts) == 2:
                            tag_key = tag_parts[0].strip()
                            tag_value = tag_parts[1].strip()
                            new_row[f"mgn:launch:tag:instance:{tag_key}"] = tag_value

            new_rows.append(new_row)

        # Create a new dataframe with the template columns and new data
        if new_rows:
            result_df = pd.DataFrame(new_rows)

            # Get all unique tag columns that were created
            tag_columns = [col for col in result_df.columns if col.startswith("mgn:launch:tag:instance:")]

            # Combine template columns with dynamic tag columns
            all_columns = template_columns + tag_columns

            # Ensure all columns exist (fill missing ones with empty strings)
            for col in all_columns:
                if col not in result_df.columns:
                    result_df[col] = ""

            # Reorder columns to match template + tags
            result_df = result_df.reindex(columns=all_columns, fill_value="")
        else:
            # Create empty dataframe with correct template columns
            result_df = pd.DataFrame(columns=template_columns)

        result_df.to_csv(out_path,index=False)
        error = ""
    except:
        error = "Please Enter correct files or select valid movegroup."

    if os.path.isfile(out_path) and error == "":
        with open(out_path,'r') as f:
            x = len(f.readlines())
        if x == 1 or x == 0:
            msg = "No Vms found for provided movegroup or check the treatment is set to Rehost."
        else:
            msg = "csv file created and downloaded successfully."
        return out_path,"",msg,0
    else:
        err1 = "Please check whether you have uploaded the proper files."
        log_path = os.path.join(rootpath,'templates','log.html')
        with open(log_path, 'w') as f:
            f.write('<!DOCTYPE html>\n<html>\n<head>\n<style>\n.body {\nfont-family: Arial,\nsans-serif;}\n.text {\nfont-size: 16px;\nline-height: 1.5;\npadding: 20px;\nbackground-color: #f5f5f5;\nborder: 1px solid #ccc;}\n.scroll{\noverflow-y: scroll;}\n</style>\n<title>TFVARS Generation detailed log</title>\n</head>\n<body>\n<div class="body text scroll">\n<pre>'+error+'</pre>\n</div>\n</body>\n</html>')
        f.close()
        return "",error,err1,2
    

            
            
